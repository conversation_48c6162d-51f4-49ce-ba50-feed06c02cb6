# Server Configuration
PORT=8080

# Database Configuration
DB_URL=************************************************************************************************************************************
DB_USERNAME=root
DB_PASSWORD=password
DDL_AUTO=update
SHOW_SQL=false

# JWT Configuration
JWT_SECRET=mySecretKey123456789012345678901234567890
JWT_ACCESS_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# File Upload Configuration
MAX_FILE_SIZE=10MB
MAX_REQUEST_SIZE=10MB
UPLOAD_DIR=./uploads

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Logging Configuration
LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=WARN

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# ML Service Configuration
ML_SERVICE_URL=http://localhost:8001

# Chatbot Configuration
CHATBOT_SERVICE_URL=http://localhost:8002
