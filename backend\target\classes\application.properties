# Server Configuration
server.port=${PORT:8080}
server.servlet.context-path=/api

# Database Configuration
spring.datasource.url=${DB_URL:************************************************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:password}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=${DDL_AUTO:update}
spring.jpa.show-sql=${SHOW_SQL:false}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
jwt.access-token-expiration=${JWT_ACCESS_EXPIRATION:86400000}
jwt.refresh-token-expiration=${JWT_REFRESH_EXPIRATION:604800000}

# File Upload Configuration
spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:10MB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:10MB}
file.upload-dir=${UPLOAD_DIR:./uploads}

# Email Configuration
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# Logging Configuration
logging.level.com.medreserve=${LOG_LEVEL:INFO}
logging.level.org.springframework.security=${SECURITY_LOG_LEVEL:WARN}

# CORS Configuration
cors.allowed-origins=${CORS_ORIGINS:http://localhost:3000,http://localhost:5173}

# ML Service Configuration
ml.service.url=${ML_SERVICE_URL:http://localhost:8001}

# Chatbot Configuration
chatbot.service.url=${CHATBOT_SERVICE_URL:http://localhost:8002}
